"""
Database configuration and models for the AI Backend.
"""

import uuid
from datetime import datetime
from typing import List, Optional
from decimal import Decimal

from sqlalchemy import (
    <PERSON><PERSON>an, Column, DateTime, Integer, String, Text, 
    DECIMAL, ARRAY, create_engine, MetaData
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import func

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    database_url: str = Field(
        "postgresql://ai_user:ai_password@localhost:5432/ai_backend",
        env="DATABASE_URL"
    )
    echo_sql: bool = Field(False, env="DATABASE_ECHO_SQL")
    
    class Config:
        env_file = ".env"


# Database setup
settings = DatabaseSettings()
engine = create_engine(settings.database_url, echo=settings.echo_sql)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


class ModelConfig(Base):
    """Model configuration table."""
    
    __tablename__ = "model_configs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, unique=True)
    provider = Column(String(100), nullable=False)
    price_input_per_1k = Column(DECIMAL(10, 6), default=0.0)
    price_output_per_1k = Column(DECIMAL(10, 6), default=0.0)
    max_context_tokens = Column(Integer, default=4096)
    tier = Column(String(50), default='free')
    is_enabled = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class Provider(Base):
    """Provider configuration table."""
    
    __tablename__ = "providers"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, unique=True)
    base_url = Column(String(500))
    is_enabled = Column(Boolean, default=True)
    priority = Column(Integer, default=1)
    rate_limit_rpm = Column(Integer)
    rate_limit_tpm = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class RoutingPolicy(Base):
    """Routing policy configuration table."""
    
    __tablename__ = "routing_policies"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_class = Column(String(50), nullable=False)
    score_threshold = Column(DECIMAL(3, 2), nullable=False)
    preferred_models = Column(ARRAY(String))
    fallback_models = Column(ARRAY(String))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class UsageLog(Base):
    """Usage logging table."""
    
    __tablename__ = "usage_logs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(String(255))
    model_used = Column(String(255), nullable=False)
    provider_used = Column(String(100), nullable=False)
    tokens_input = Column(Integer, default=0)
    tokens_output = Column(Integer, default=0)
    cost_usd = Column(DECIMAL(10, 6), default=0.0)
    latency_ms = Column(Integer)
    task_class = Column(String(50))
    complexity_score = Column(DECIMAL(3, 2))
    success = Column(Boolean, default=True)
    error_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


# Pydantic models for API
class ModelConfigSchema(BaseModel):
    """Pydantic model for ModelConfig."""
    
    id: Optional[uuid.UUID] = None
    name: str
    provider: str
    price_input_per_1k: Decimal = Field(default=Decimal('0.0'))
    price_output_per_1k: Decimal = Field(default=Decimal('0.0'))
    max_context_tokens: int = 4096
    tier: str = 'free'
    is_enabled: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ProviderSchema(BaseModel):
    """Pydantic model for Provider."""
    
    id: Optional[uuid.UUID] = None
    name: str
    base_url: Optional[str] = None
    is_enabled: bool = True
    priority: int = 1
    rate_limit_rpm: Optional[int] = None
    rate_limit_tpm: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class RoutingPolicySchema(BaseModel):
    """Pydantic model for RoutingPolicy."""
    
    id: Optional[uuid.UUID] = None
    task_class: str
    score_threshold: Decimal
    preferred_models: List[str]
    fallback_models: List[str]
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UsageLogSchema(BaseModel):
    """Pydantic model for UsageLog."""
    
    id: Optional[uuid.UUID] = None
    conversation_id: Optional[str] = None
    model_used: str
    provider_used: str
    tokens_input: int = 0
    tokens_output: int = 0
    cost_usd: Decimal = Field(default=Decimal('0.0'))
    latency_ms: Optional[int] = None
    task_class: Optional[str] = None
    complexity_score: Optional[Decimal] = None
    success: bool = True
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """Create all tables."""
    Base.metadata.create_all(bind=engine)
