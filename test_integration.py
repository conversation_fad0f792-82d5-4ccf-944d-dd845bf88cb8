#!/usr/bin/env python3
"""
Simple integration test for multi-provider functionality.
"""

import os
import sys
import asyncio
from typing import Dict, Any

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_configuration():
    """Test that configuration loads correctly."""
    print("🔧 Testing configuration...")
    try:
        from app.config import get_settings
        settings = get_settings()
        print(f"✅ Configuration loaded: {settings.app_name}")
        print(f"   Primary provider: {settings.primary_provider}")
        print(f"   Available providers: {settings.get_available_providers()}")
        return True
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        return False

def test_provider_factory():
    """Test that provider factory works."""
    print("\n🏭 Testing provider factory...")
    try:
        from app.providers import get_provider_factory
        factory = get_provider_factory()
        print(f"✅ Provider factory created")
        print(f"   Configured providers: {list(factory.provider_configs.keys())}")
        
        # Test getting available models
        models = factory.get_available_models()
        print(f"   Available models: {models}")
        
        # Test getting recommended model
        provider, model = factory.get_recommended_model()
        print(f"   Recommended: {provider} / {model}")
        
        return True
    except Exception as e:
        print(f"❌ Provider factory failed: {e}")
        return False

def test_agent_creation():
    """Test that agent can be created."""
    print("\n🤖 Testing agent creation...")
    try:
        from app.agent import PydanticAIAgent
        from app.config import ProviderType
        
        agent = PydanticAIAgent(
            provider=ProviderType.OPENAI,
            model_name="gpt-4o-mini"
        )
        print(f"✅ Agent created successfully")
        print(f"   Provider: {agent.provider}")
        print(f"   Model: {agent.model_name}")
        
        # Test provider info
        info = agent.get_provider_info()
        print(f"   Provider info: {info}")
        
        return True
    except Exception as e:
        print(f"❌ Agent creation failed: {e}")
        return False

def test_api_endpoints():
    """Test that API endpoints work."""
    print("\n🌐 Testing API endpoints...")
    try:
        from app.main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/health")
        print(f"✅ Health endpoint: {response.status_code}")
        if response.status_code == 200:
            health_data = response.json()
            print(f"   Status: {health_data.get('status')}")
        
        # Test providers endpoint
        response = client.get("/providers")
        print(f"✅ Providers endpoint: {response.status_code}")
        if response.status_code == 200:
            providers_data = response.json()
            print(f"   Current provider: {providers_data.get('current_provider')}")
            print(f"   Available providers: {list(providers_data.get('available_providers', {}).keys())}")
        
        return True
    except Exception as e:
        print(f"❌ API endpoints failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting Multi-Provider Integration Tests\n")
    
    tests = [
        test_configuration,
        test_provider_factory,
        test_agent_creation,
        test_api_endpoints,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Multi-provider system is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
