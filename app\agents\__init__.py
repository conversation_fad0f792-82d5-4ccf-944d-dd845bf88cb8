"""
Specialized AI agents for different use cases.

This package contains specialized agents that use intelligent model routing
to provide cost-efficient, high-quality responses for specific domains.
"""

from .chatbot_agent import chatbot_agent, ChatbotAgent
from .rag_agent import rag_agent, RAGAgent
from .image_agent import image_agent, ImageAgent
from .document_agent import document_agent, DocumentAgent
from .marketing_agent import marketing_agent, MarketingAgent
from .automation_agent import automation_agent, AutomationAgent

__all__ = [
    # Agent instances (ready to use)
    "chatbot_agent",
    "rag_agent",
    "image_agent",
    "document_agent",
    "marketing_agent",
    "automation_agent",

    # Agent classes (for custom instantiation)
    "ChatbotAgent",
    "RAGAgent",
    "ImageAgent",
    "DocumentAgent",
    "MarketingAgent",
    "AutomationAgent",
]


def get_agent(agent_type: str):
    """
    Get an agent instance by type.

    Args:
        agent_type: Type of agent to retrieve

    Returns:
        Agent instance

    Raises:
        ValueError: If agent type is not recognized
    """
    agents = {
        "chatbot": chatbot_agent,
        "rag": rag_agent,
        "image": image_agent,
        "document": document_agent,
        "marketing": marketing_agent,
        "automation": automation_agent,
    }

    if agent_type not in agents:
        available = ", ".join(agents.keys())
        raise ValueError(f"Unknown agent type: {agent_type}. Available: {available}")

    return agents[agent_type]


def list_available_agents():
    """
    List all available agent types.

    Returns:
        List of available agent type names
    """
    return [
        "chatbot",
        "rag",
        "image",
        "document",
        "marketing",
        "automation",
    ]


def get_agent_info(agent_type: str):
    """
    Get information about a specific agent type.

    Args:
        agent_type: Type of agent to get info for

    Returns:
        Dictionary with agent information
    """
    agent_info = {
        "chatbot": {
            "name": "Chatbot Agent",
            "description": "General-purpose conversational AI with optional RAG capabilities",
            "use_cases": ["Customer support", "General Q&A", "Interactive conversations"],
            "features": ["Multi-model routing", "Conversation history", "Optional RAG"]
        },
        "rag": {
            "name": "RAG Agent",
            "description": "Retrieval-Augmented Generation for knowledge-enhanced responses",
            "use_cases": ["Knowledge base queries", "Document search", "Fact-based Q&A"],
            "features": ["Vector search", "Source citation", "Multi-source retrieval"]
        },
        "image": {
            "name": "Image Agent",
            "description": "Vision-language model for image analysis and understanding",
            "use_cases": ["Image description", "OCR", "Visual Q&A", "Object detection"],
            "features": ["Multi-modal processing", "Vision model routing", "Image analysis tools"]
        },
        "document": {
            "name": "Document Agent",
            "description": "Document processing, analysis, and generation specialist",
            "use_cases": ["Document analysis", "Content generation", "Editing", "Summarization"],
            "features": ["Structure analysis", "Format optimization", "Content generation"]
        },
        "marketing": {
            "name": "Marketing Agent",
            "description": "Marketing content creation and campaign planning specialist",
            "use_cases": ["Social media posts", "Campaign planning", "Ad copy", "Content strategy"],
            "features": ["Platform optimization", "Brand voice consistency", "Campaign planning"]
        },
        "automation": {
            "name": "Automation Agent",
            "description": "Workflow automation and process optimization specialist",
            "use_cases": ["Workflow design", "API integration", "Process automation", "Task scheduling"],
            "features": ["Workflow design", "Code generation", "Integration templates", "Scheduling"]
        }
    }

    if agent_type not in agent_info:
        available = ", ".join(agent_info.keys())
        raise ValueError(f"Unknown agent type: {agent_type}. Available: {available}")

    return agent_info[agent_type]
