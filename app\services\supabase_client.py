"""
Supabase client service for authentication, database, and storage operations.
"""

import logging
from typing import Optional, Dict, Any, List
from supabase import create_client, Client
from gotrue.errors import AuthError
from postgrest.exceptions import APIError

from ..config import get_settings

logger = logging.getLogger(__name__)


class SupabaseService:
    """Centralized Supabase service for all database, auth, and storage operations."""
    
    def __init__(self):
        """Initialize Supabase client."""
        self.settings = get_settings()
        self._client: Optional[Client] = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize the Supabase client."""
        try:
            if not self.settings.is_supabase_configured():
                logger.warning("Supabase is not configured. Some features will be unavailable.")
                return
            
            config = self.settings.get_supabase_config()
            self._client = create_client(config["url"], config["key"])
            logger.info("Supabase client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Supabase client: {e}")
            self._client = None
    
    @property
    def client(self) -> Client:
        """Get the Supabase client."""
        if self._client is None:
            raise RuntimeError("Supabase client is not initialized. Check your configuration.")
        return self._client
    
    @property
    def is_available(self) -> bool:
        """Check if Supabase client is available."""
        return self._client is not None
    
    def health_check(self) -> Dict[str, Any]:
        """Perform a health check on Supabase connection."""
        if not self.is_available:
            return {
                "status": "unavailable",
                "message": "Supabase client not initialized"
            }
        
        try:
            # Try a simple query to test connection
            response = self.client.table("_health_check").select("*").limit(1).execute()
            return {
                "status": "healthy",
                "message": "Supabase connection successful",
                "url": self.settings.supabase_url
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"Supabase connection failed: {str(e)}",
                "url": self.settings.supabase_url
            }


# Global Supabase service instance
_supabase_service: Optional[SupabaseService] = None


def get_supabase_service() -> SupabaseService:
    """Get the global Supabase service instance."""
    global _supabase_service
    if _supabase_service is None:
        _supabase_service = SupabaseService()
    return _supabase_service


def get_supabase_client() -> Client:
    """Get the Supabase client directly."""
    return get_supabase_service().client


class SupabaseError(Exception):
    """Base exception for Supabase operations."""
    pass


class SupabaseAuthError(SupabaseError):
    """Exception for authentication errors."""
    pass


class SupabaseDBError(SupabaseError):
    """Exception for database errors."""
    pass


class SupabaseStorageError(SupabaseError):
    """Exception for storage errors."""
    pass


def handle_supabase_error(func):
    """Decorator to handle Supabase errors and convert them to custom exceptions."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except AuthError as e:
            logger.error(f"Supabase auth error in {func.__name__}: {e}")
            raise SupabaseAuthError(f"Authentication error: {e}")
        except APIError as e:
            logger.error(f"Supabase API error in {func.__name__}: {e}")
            raise SupabaseDBError(f"Database error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {e}")
            raise SupabaseError(f"Supabase operation failed: {e}")
    return wrapper
