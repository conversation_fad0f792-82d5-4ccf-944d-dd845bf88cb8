"""
Automation agent for workflow automation, task scheduling, and process optimization.
"""

import logging
from typing import Optional, Dict, Any, List, Union
from datetime import datetime, timedelta

from pydantic_ai import Agent, RunContext
from pydantic import BaseModel

from ..config import get_settings
from ..services.router_service import router_service
from ..services.workflow_engine import workflow_engine
from ..services.observability import observability_service, SpanMetadata
from ..providers import get_provider_factory
from ..models import ChatResponse, ChatMessage


logger = logging.getLogger(__name__)


class AutomationContext(BaseModel):
    """Context for automation tasks."""
    conversation_id: str
    automation_type: str = "workflow"
    complexity_level: str = "medium"
    target_system: Optional[str] = None
    schedule_requirements: Dict[str, Any] = {}


class WorkflowStep(BaseModel):
    """Represents a step in an automated workflow."""
    step_id: str
    name: str
    description: str
    action_type: str  # api_call, data_processing, notification, etc.
    parameters: Dict[str, Any] = {}
    dependencies: List[str] = []
    estimated_duration: Optional[int] = None  # in seconds


class AutomationAgent:
    """
    Specialized agent for automation, workflow design, and process optimization.
    
    This agent helps design and implement automated workflows, API integrations,
    and business process automation using intelligent model routing.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.provider_factory = get_provider_factory()
        self.conversations: Dict[str, List[ChatMessage]] = {}
        self.workflows: Dict[str, List[WorkflowStep]] = {}
        
        # Initialize with a default model (will be overridden by router)
        self.current_model = self.provider_factory.create_model()
        
        # Create the agent
        self.agent = Agent(
            model=self.current_model,
            system_prompt=self._get_system_prompt(),
        )
        
        self._register_tools()
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for the automation agent."""
        return """You are an expert automation and workflow specialist with deep knowledge in:

Workflow Automation:
- Business process automation design
- API integration and orchestration
- Data pipeline creation and management
- Task scheduling and job automation
- Error handling and retry mechanisms

System Integration:
- REST API design and consumption
- Webhook implementation
- Database automation
- File processing workflows
- Email and notification automation

Process Optimization:
- Workflow efficiency analysis
- Bottleneck identification
- Performance optimization
- Cost reduction strategies
- Scalability planning

Technical Implementation:
- Python automation scripts
- Cron job scheduling
- Docker containerization
- Cloud automation (AWS, Azure, GCP)
- CI/CD pipeline design

I design robust, scalable automation solutions that reduce manual work and improve
efficiency. I automatically select the most appropriate model for each automation
task based on complexity and technical requirements.

When designing automation:
1. Understand the current manual process
2. Identify automation opportunities
3. Design fault-tolerant workflows
4. Include proper error handling
5. Plan for monitoring and maintenance
6. Consider security and compliance
7. Optimize for performance and cost
"""
    
    def _register_tools(self):
        """Register automation-specific tools."""
        
        @self.agent.tool
        def analyze_process(ctx: RunContext[AutomationContext], process_description: str) -> str:
            """Analyze a manual process for automation opportunities."""
            steps = process_description.split('\n')
            automatable_steps = []
            manual_steps = []
            
            for i, step in enumerate(steps, 1):
                if any(keyword in step.lower() for keyword in ['copy', 'paste', 'click', 'type', 'download', 'upload']):
                    automatable_steps.append(f"Step {i}: {step.strip()}")
                else:
                    manual_steps.append(f"Step {i}: {step.strip()}")
            
            return f"""Process Analysis:
Automation Type: {ctx.deps.automation_type}
Complexity: {ctx.deps.complexity_level}

Automatable Steps ({len(automatable_steps)}):
{chr(10).join(automatable_steps) if automatable_steps else 'None identified'}

Manual Steps Remaining ({len(manual_steps)}):
{chr(10).join(manual_steps) if manual_steps else 'All steps can be automated'}

Automation Potential: {len(automatable_steps) / len(steps) * 100:.1f}%
"""
        
        @self.agent.tool
        def create_workflow_steps(ctx: RunContext[AutomationContext], workflow_name: str, requirements: str) -> str:
            """Create detailed workflow steps for automation."""
            # Generate workflow ID
            workflow_id = f"wf_{workflow_name.lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}"
            
            # Create sample workflow steps (in production, this would be more sophisticated)
            steps = [
                WorkflowStep(
                    step_id=f"{workflow_id}_step_1",
                    name="Initialize Workflow",
                    description="Set up workflow environment and validate inputs",
                    action_type="initialization",
                    parameters={"validate_inputs": True, "setup_logging": True}
                ),
                WorkflowStep(
                    step_id=f"{workflow_id}_step_2",
                    name="Process Data",
                    description="Execute main workflow logic",
                    action_type="data_processing",
                    parameters={"requirements": requirements},
                    dependencies=[f"{workflow_id}_step_1"]
                ),
                WorkflowStep(
                    step_id=f"{workflow_id}_step_3",
                    name="Finalize and Notify",
                    description="Complete workflow and send notifications",
                    action_type="notification",
                    parameters={"send_completion_email": True},
                    dependencies=[f"{workflow_id}_step_2"]
                )
            ]
            
            # Store workflow
            self.workflows[workflow_id] = steps
            
            steps_text = []
            for step in steps:
                deps = f" (depends on: {', '.join(step.dependencies)})" if step.dependencies else ""
                steps_text.append(f"- {step.name}: {step.description}{deps}")
            
            return f"""Workflow Created: {workflow_name}
ID: {workflow_id}
Steps:
{chr(10).join(steps_text)}

Total Steps: {len(steps)}
Estimated Duration: {sum(step.estimated_duration or 60 for step in steps)} seconds
"""
        
        @self.agent.tool
        def generate_api_integration(ctx: RunContext[AutomationContext], api_description: str, integration_type: str) -> str:
            """Generate API integration code template."""
            templates = {
                "rest_client": """
import requests
import json
from typing import Dict, Any

class APIClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def get(self, endpoint: str) -> Dict[str, Any]:
        response = requests.get(f'{self.base_url}/{endpoint}', headers=self.headers)
        response.raise_for_status()
        return response.json()
    
    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        response = requests.post(
            f'{self.base_url}/{endpoint}', 
            headers=self.headers,
            json=data
        )
        response.raise_for_status()
        return response.json()
""",
                "webhook": """
from flask import Flask, request, jsonify
import logging

app = Flask(__name__)
logger = logging.getLogger(__name__)

@app.route('/webhook', methods=['POST'])
def handle_webhook():
    try:
        data = request.get_json()
        logger.info(f'Received webhook: {data}')
        
        # Process webhook data
        result = process_webhook_data(data)
        
        return jsonify({'status': 'success', 'result': result})
    except Exception as e:
        logger.error(f'Webhook error: {e}')
        return jsonify({'status': 'error', 'message': str(e)}), 500

def process_webhook_data(data):
    # Implement your webhook processing logic here
    return {'processed': True}
"""
            }
            
            template = templates.get(integration_type, templates["rest_client"])
            
            return f"""API Integration Template for: {api_description}
Type: {integration_type}

{template}

# Usage Example:
# client = APIClient('https://api.example.com', 'your-api-key')
# result = client.get('endpoint')
"""
        
        @self.agent.tool
        def schedule_automation(ctx: RunContext[AutomationContext], task_name: str, schedule_expression: str) -> str:
            """Generate scheduling configuration for automation tasks."""
            schedule_types = {
                "cron": f"# Cron job for {task_name}\n{schedule_expression} /path/to/script.py",
                "systemd": f"""# Systemd timer for {task_name}
[Unit]
Description={task_name}

[Timer]
OnCalendar={schedule_expression}
Persistent=true

[Install]
WantedBy=timers.target""",
                "python": f"""# Python APScheduler for {task_name}
from apscheduler.schedulers.blocking import BlockingScheduler

scheduler = BlockingScheduler()

@scheduler.scheduled_job('{schedule_expression}')
def {task_name.lower().replace(' ', '_')}():
    # Your automation logic here
    pass

scheduler.start()"""
            }
            
            # Detect schedule type
            if schedule_expression.count(' ') >= 4:
                schedule_type = "cron"
            elif ":" in schedule_expression:
                schedule_type = "systemd"
            else:
                schedule_type = "python"
            
            return f"""Scheduling Configuration for: {task_name}
Schedule: {schedule_expression}
Type: {schedule_type}

{schedule_types[schedule_type]}
"""
    
    async def design_workflow(
        self,
        process_description: str,
        conversation_id: str,
        automation_type: str = "workflow",
        complexity_level: str = "medium",
        target_system: Optional[str] = None,
        **kwargs
    ) -> ChatResponse:
        """
        Design an automated workflow based on process description.
        
        Args:
            process_description: Description of the process to automate
            conversation_id: Conversation identifier
            automation_type: Type of automation (workflow, api, data_pipeline, etc.)
            complexity_level: Complexity level (simple, medium, complex)
            target_system: Target system for automation
            **kwargs: Additional parameters
        """
        try:
            enhanced_prompt = f"""Design an automated {automation_type} for the following process:

Process Description: {process_description}
Complexity Level: {complexity_level}
Target System: {target_system or 'General'}

Please provide:
1. Process analysis and automation opportunities
2. Detailed workflow steps
3. Technical implementation approach
4. Error handling and monitoring
5. Deployment and maintenance considerations
"""
            
            # Route to appropriate model
            routing_result = router_service.pick(
                text=enhanced_prompt,
                modality="text",
                preferred_provider=kwargs.get('preferred_provider'),
                max_cost=kwargs.get('max_cost')
            )
            
            # Update model if needed
            if routing_result.selected_model != getattr(self.current_model, 'model_name', ''):
                logger.info(f"Automation agent switching to model: {routing_result.selected_model}")
                self.current_model = self.provider_factory.create_model(
                    provider_type=routing_result.provider,
                    model_name=routing_result.selected_model
                )
                
                self.agent = Agent(
                    model=self.current_model,
                    system_prompt=self._get_system_prompt(),
                )
                self._register_tools()
            
            # Create context
            context = AutomationContext(
                conversation_id=conversation_id,
                automation_type=automation_type,
                complexity_level=complexity_level,
                target_system=target_system
            )
            
            # Generate workflow design
            result = await self.agent.run(enhanced_prompt, deps=context)
            
            # Store messages
            user_message = ChatMessage(role="user", content=f"Design {automation_type}: {process_description}")
            assistant_message = ChatMessage(role="assistant", content=result.data)
            
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = []
            self.conversations[conversation_id].extend([user_message, assistant_message])
            
            # Create response
            usage = result.usage()
            response = ChatResponse(
                response=result.data,
                conversation_id=conversation_id,
                model_used=f"{routing_result.provider}:{routing_result.selected_model}",
                tokens_used=usage.total_tokens,
                metadata={
                    "routing_reason": routing_result.reason,
                    "estimated_cost": float(routing_result.estimated_cost),
                    "automation_type": automation_type,
                    "complexity_level": complexity_level,
                    "target_system": target_system,
                    "agent_type": "automation"
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in automation workflow design: {e}")
            raise
    
    def get_workflow(self, workflow_id: str) -> Optional[List[WorkflowStep]]:
        """Get a stored workflow by ID."""
        return self.workflows.get(workflow_id)
    
    def list_workflows(self) -> List[str]:
        """List all stored workflow IDs."""
        return list(self.workflows.keys())

    async def create_workflow(
        self,
        name: str,
        description: str,
        steps: List[Dict[str, Any]],
        conversation_id: str = "default",
        **kwargs
    ) -> Dict[str, Any]:
        """Create a new executable workflow using the workflow engine."""
        start_time = time.time()

        metadata_span = SpanMetadata(
            operation_type="workflow_creation",
            workflow_name=name,
            conversation_id=conversation_id
        )

        async with observability_service.trace_operation("automation_create_workflow", metadata_span) as span:
            try:
                # Create workflow using the workflow engine
                workflow_id = workflow_engine.create_workflow(
                    name=name,
                    description=description,
                    steps=steps,
                    metadata={
                        "created_by": "automation_agent",
                        "conversation_id": conversation_id,
                        "created_at": time.time()
                    }
                )

                duration_ms = (time.time() - start_time) * 1000

                result = {
                    "workflow_id": workflow_id,
                    "name": name,
                    "description": description,
                    "total_steps": len(steps),
                    "status": "created",
                    "creation_time_ms": duration_ms
                }

                # Log workflow creation
                observability_service.log_agent_execution(
                    agent_type="automation",
                    request_data={
                        "operation": "create_workflow",
                        "name": name,
                        "total_steps": len(steps)
                    },
                    response_data=result,
                    duration_ms=duration_ms,
                    model_name="workflow_engine",
                    provider="internal"
                )

                return result

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                logger.error(f"Error creating workflow {name}: {e}")

                observability_service.log_error(
                    operation_name="automation_create_workflow",
                    error=e,
                    context={
                        "workflow_name": name,
                        "total_steps": len(steps),
                        "duration_ms": duration_ms
                    }
                )

                return {
                    "status": "error",
                    "message": str(e),
                    "workflow_name": name,
                    "creation_time_ms": duration_ms
                }

    async def execute_workflow(
        self,
        workflow_id: str,
        conversation_id: str = "default",
        **kwargs
    ) -> Dict[str, Any]:
        """Execute a workflow using the workflow engine."""
        start_time = time.time()

        metadata_span = SpanMetadata(
            operation_type="workflow_execution",
            workflow_id=workflow_id,
            conversation_id=conversation_id
        )

        async with observability_service.trace_operation("automation_execute_workflow", metadata_span) as span:
            try:
                # Execute workflow using the workflow engine
                result = await workflow_engine.execute_workflow(workflow_id)

                duration_ms = (time.time() - start_time) * 1000

                # Log workflow execution
                observability_service.log_agent_execution(
                    agent_type="automation",
                    request_data={
                        "operation": "execute_workflow",
                        "workflow_id": workflow_id
                    },
                    response_data=result,
                    duration_ms=duration_ms,
                    model_name="workflow_engine",
                    provider="internal"
                )

                return result

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                logger.error(f"Error executing workflow {workflow_id}: {e}")

                observability_service.log_error(
                    operation_name="automation_execute_workflow",
                    error=e,
                    context={
                        "workflow_id": workflow_id,
                        "duration_ms": duration_ms
                    }
                )

                return {
                    "status": "error",
                    "message": str(e),
                    "workflow_id": workflow_id,
                    "execution_time_ms": duration_ms
                }

    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of a workflow."""
        try:
            return workflow_engine.get_workflow_status(workflow_id)
        except Exception as e:
            logger.error(f"Error getting workflow status for {workflow_id}: {e}")
            return None

    async def list_engine_workflows(self) -> List[Dict[str, Any]]:
        """List all workflows in the workflow engine."""
        try:
            return workflow_engine.list_workflows()
        except Exception as e:
            logger.error(f"Error listing workflows: {e}")
            return []

    async def design_and_execute_workflow(
        self,
        task_description: str,
        conversation_id: str = "default",
        auto_execute: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Design a workflow based on task description and optionally execute it."""
        start_time = time.time()

        metadata_span = SpanMetadata(
            operation_type="workflow_design_and_execute",
            task_description=task_description[:100],
            conversation_id=conversation_id
        )

        async with observability_service.trace_operation("automation_design_execute", metadata_span) as span:
            try:
                # First, design the workflow using AI
                design_response = await self.design_workflow(
                    task_description=task_description,
                    conversation_id=conversation_id,
                    **kwargs
                )

                if design_response.metadata.get("error"):
                    return {
                        "status": "error",
                        "message": "Failed to design workflow",
                        "design_error": design_response.metadata.get("error")
                    }

                # Extract workflow steps from the AI response
                # This would need more sophisticated parsing in production
                workflow_name = f"Auto-generated workflow for: {task_description[:50]}..."
                workflow_description = f"Automatically designed workflow based on: {task_description}"

                # Create a simple workflow structure
                # In production, this would parse the AI response to extract actual steps
                steps = [
                    {
                        "id": "step_1",
                        "name": "Log Start",
                        "action": "log",
                        "parameters": {
                            "message": f"Starting workflow for: {task_description}",
                            "level": "info"
                        }
                    },
                    {
                        "id": "step_2",
                        "name": "Process Task",
                        "action": "log",
                        "parameters": {
                            "message": f"Processing: {task_description}",
                            "level": "info"
                        },
                        "dependencies": ["step_1"]
                    },
                    {
                        "id": "step_3",
                        "name": "Log Completion",
                        "action": "log",
                        "parameters": {
                            "message": "Workflow completed successfully",
                            "level": "info"
                        },
                        "dependencies": ["step_2"]
                    }
                ]

                # Create the workflow
                create_result = await self.create_workflow(
                    name=workflow_name,
                    description=workflow_description,
                    steps=steps,
                    conversation_id=conversation_id
                )

                if create_result.get("status") == "error":
                    return create_result

                workflow_id = create_result["workflow_id"]

                # Execute if requested
                execution_result = None
                if auto_execute:
                    execution_result = await self.execute_workflow(
                        workflow_id=workflow_id,
                        conversation_id=conversation_id
                    )

                duration_ms = (time.time() - start_time) * 1000

                result = {
                    "status": "success",
                    "workflow_id": workflow_id,
                    "design_response": design_response.message,
                    "workflow_created": create_result,
                    "execution_result": execution_result,
                    "auto_executed": auto_execute,
                    "total_time_ms": duration_ms
                }

                return result

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                logger.error(f"Error in design and execute workflow: {e}")

                observability_service.log_error(
                    operation_name="automation_design_execute",
                    error=e,
                    context={
                        "task_description": task_description,
                        "auto_execute": auto_execute,
                        "duration_ms": duration_ms
                    }
                )

                return {
                    "status": "error",
                    "message": str(e),
                    "task_description": task_description,
                    "total_time_ms": duration_ms
                }


# Global automation agent instance
automation_agent = AutomationAgent()
