"""
Document agent for document processing, analysis, and generation tasks.
"""

import logging
from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic_ai import Agent, RunContext
from pydantic import BaseModel

from ..config import get_settings
from ..services.router_service import router_service
from ..providers import get_provider_factory
from ..models import ChatResponse, ChatMessage


logger = logging.getLogger(__name__)


class DocumentContext(BaseModel):
    """Context for document processing tasks."""
    conversation_id: str
    document_type: str = "general"
    processing_mode: str = "analysis"
    word_count_target: Optional[int] = None
    format_requirements: Dict[str, Any] = {}


class DocumentAgent:
    """
    Specialized agent for document-related tasks including analysis, generation, and editing.
    
    This agent uses intelligent routing to select appropriate models based on document
    complexity and length requirements.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.provider_factory = get_provider_factory()
        self.conversations: Dict[str, List[ChatMessage]] = {}
        
        # Initialize with a default model (will be overridden by router)
        self.current_model = self.provider_factory.create_model()
        
        # Create the agent
        self.agent = Agent(
            model=self.current_model,
            system_prompt=self._get_system_prompt(),
        )
        
        self._register_tools()
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for the document agent."""
        return """You are an expert document processing and writing assistant with advanced capabilities in:

Document Analysis:
- Content analysis and summarization
- Structure and organization assessment
- Style and tone evaluation
- Fact-checking and verification
- Citation and reference validation

Document Generation:
- Professional reports and proposals
- Academic papers and research documents
- Business communications and memos
- Technical documentation
- Creative writing and content creation

Document Editing:
- Grammar and style improvement
- Content restructuring and organization
- Formatting and presentation enhancement
- Proofreading and error correction
- Version comparison and revision tracking

I automatically select the most appropriate model for each document task based on complexity,
length requirements, and quality needs while optimizing for cost efficiency.

When working with documents:
1. Analyze the document type and requirements
2. Provide structured, well-organized output
3. Maintain appropriate tone and style
4. Follow formatting guidelines
5. Ensure accuracy and clarity
6. Cite sources when applicable
"""
    
    def _register_tools(self):
        """Register document processing tools."""
        
        @self.agent.tool
        def analyze_document_structure(ctx: RunContext[DocumentContext], document_text: str) -> str:
            """Analyze the structure and organization of a document."""
            lines = document_text.split('\n')
            paragraphs = [p for p in document_text.split('\n\n') if p.strip()]
            
            analysis = f"""Document Structure Analysis:
- Total lines: {len(lines)}
- Paragraphs: {len(paragraphs)}
- Word count: ~{len(document_text.split())}
- Document type: {ctx.deps.document_type}
- Processing mode: {ctx.deps.processing_mode}
"""
            return analysis
        
        @self.agent.tool
        def extract_key_points(ctx: RunContext[DocumentContext], document_text: str, max_points: int = 10) -> str:
            """Extract key points from a document."""
            # Simple implementation - in production would use more sophisticated NLP
            sentences = document_text.split('. ')
            key_sentences = sentences[:max_points]  # Simplified extraction
            
            points = []
            for i, sentence in enumerate(key_sentences, 1):
                if sentence.strip():
                    points.append(f"{i}. {sentence.strip()}")
            
            return "Key Points:\n" + "\n".join(points)
        
        @self.agent.tool
        def check_formatting(ctx: RunContext[DocumentContext], document_text: str) -> str:
            """Check document formatting and suggest improvements."""
            issues = []
            
            if not document_text.strip():
                issues.append("Document is empty")
            
            lines = document_text.split('\n')
            if len(lines) < 3:
                issues.append("Document may be too short")
            
            if not any(line.strip().endswith('.') for line in lines):
                issues.append("No proper sentence endings found")
            
            if len(issues) == 0:
                return "Document formatting appears to be good."
            
            return "Formatting issues found:\n" + "\n".join(f"- {issue}" for issue in issues)
        
        @self.agent.tool
        def generate_outline(ctx: RunContext[DocumentContext], topic: str, document_type: str) -> str:
            """Generate an outline for a document."""
            outlines = {
                "report": [
                    "1. Executive Summary",
                    "2. Introduction",
                    "3. Methodology",
                    "4. Findings",
                    "5. Analysis",
                    "6. Recommendations",
                    "7. Conclusion"
                ],
                "essay": [
                    "1. Introduction",
                    "2. Thesis Statement",
                    "3. Body Paragraph 1",
                    "4. Body Paragraph 2",
                    "5. Body Paragraph 3",
                    "6. Conclusion"
                ],
                "proposal": [
                    "1. Executive Summary",
                    "2. Problem Statement",
                    "3. Proposed Solution",
                    "4. Implementation Plan",
                    "5. Budget and Timeline",
                    "6. Expected Outcomes"
                ]
            }
            
            outline = outlines.get(document_type.lower(), outlines["report"])
            return f"Outline for {document_type} on '{topic}':\n" + "\n".join(outline)
    
    async def analyze_document(
        self,
        document_text: str,
        conversation_id: str,
        analysis_type: str = "comprehensive",
        document_type: str = "general",
        **kwargs
    ) -> ChatResponse:
        """
        Analyze a document and provide insights.
        
        Args:
            document_text: The document content to analyze
            conversation_id: Conversation identifier
            analysis_type: Type of analysis (summary, structure, style, etc.)
            document_type: Type of document (report, essay, proposal, etc.)
            **kwargs: Additional parameters
        """
        try:
            # Create analysis prompt based on type
            analysis_prompts = {
                "summary": f"Provide a concise summary of this {document_type} document.",
                "structure": f"Analyze the structure and organization of this {document_type}.",
                "style": f"Analyze the writing style, tone, and clarity of this {document_type}.",
                "comprehensive": f"Provide a comprehensive analysis of this {document_type} including summary, structure, style, and recommendations."
            }
            
            prompt = analysis_prompts.get(analysis_type, analysis_prompts["comprehensive"])
            full_prompt = f"{prompt}\n\nDocument:\n{document_text}"
            
            # Route to appropriate model
            routing_result = router_service.pick(
                text=full_prompt,
                modality="text",
                preferred_provider=kwargs.get('preferred_provider'),
                max_cost=kwargs.get('max_cost')
            )
            
            # Update model if needed
            if routing_result.selected_model != getattr(self.current_model, 'model_name', ''):
                logger.info(f"Document agent switching to model: {routing_result.selected_model}")
                self.current_model = self.provider_factory.create_model(
                    provider_type=routing_result.provider,
                    model_name=routing_result.selected_model
                )
                
                self.agent = Agent(
                    model=self.current_model,
                    system_prompt=self._get_system_prompt(),
                )
                self._register_tools()
            
            # Create context
            context = DocumentContext(
                conversation_id=conversation_id,
                document_type=document_type,
                processing_mode=analysis_type
            )
            
            # Store user message
            user_message = ChatMessage(
                role="user", 
                content=f"Analyze document ({analysis_type})",
                metadata={"document_type": document_type, "word_count": len(document_text.split())}
            )
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = []
            self.conversations[conversation_id].append(user_message)
            
            # Get analysis
            result = await self.agent.run(full_prompt, deps=context)
            
            # Store assistant message
            assistant_message = ChatMessage(role="assistant", content=result.data)
            self.conversations[conversation_id].append(assistant_message)
            
            # Create response
            usage = result.usage()
            response = ChatResponse(
                response=result.data,
                conversation_id=conversation_id,
                model_used=f"{routing_result.provider}:{routing_result.selected_model}",
                tokens_used=usage.total_tokens,
                metadata={
                    "routing_reason": routing_result.reason,
                    "estimated_cost": float(routing_result.estimated_cost),
                    "document_type": document_type,
                    "analysis_type": analysis_type,
                    "document_word_count": len(document_text.split()),
                    "agent_type": "document"
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in document agent analysis: {e}")
            raise
    
    async def generate_document(
        self,
        prompt: str,
        conversation_id: str,
        document_type: str = "general",
        word_count_target: Optional[int] = None,
        format_requirements: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> ChatResponse:
        """
        Generate a document based on requirements.
        
        Args:
            prompt: Description of what to generate
            conversation_id: Conversation identifier
            document_type: Type of document to generate
            word_count_target: Target word count
            format_requirements: Specific formatting requirements
            **kwargs: Additional parameters
        """
        try:
            # Enhance prompt with requirements
            enhanced_prompt = f"Generate a {document_type}: {prompt}"
            
            if word_count_target:
                enhanced_prompt += f"\n\nTarget word count: approximately {word_count_target} words"
            
            if format_requirements:
                enhanced_prompt += f"\n\nFormatting requirements: {format_requirements}"
            
            # Route to appropriate model
            routing_result = router_service.pick(
                text=enhanced_prompt,
                modality="text",
                preferred_provider=kwargs.get('preferred_provider'),
                max_cost=kwargs.get('max_cost')
            )
            
            # Update model if needed
            if routing_result.selected_model != getattr(self.current_model, 'model_name', ''):
                self.current_model = self.provider_factory.create_model(
                    provider_type=routing_result.provider,
                    model_name=routing_result.selected_model
                )
                
                self.agent = Agent(
                    model=self.current_model,
                    system_prompt=self._get_system_prompt(),
                )
                self._register_tools()
            
            # Create context
            context = DocumentContext(
                conversation_id=conversation_id,
                document_type=document_type,
                processing_mode="generation",
                word_count_target=word_count_target,
                format_requirements=format_requirements or {}
            )
            
            # Generate document
            result = await self.agent.run(enhanced_prompt, deps=context)
            
            # Store messages
            user_message = ChatMessage(role="user", content=f"Generate {document_type}: {prompt}")
            assistant_message = ChatMessage(role="assistant", content=result.data)
            
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = []
            self.conversations[conversation_id].extend([user_message, assistant_message])
            
            # Create response
            usage = result.usage()
            response = ChatResponse(
                response=result.data,
                conversation_id=conversation_id,
                model_used=f"{routing_result.provider}:{routing_result.selected_model}",
                tokens_used=usage.total_tokens,
                metadata={
                    "routing_reason": routing_result.reason,
                    "estimated_cost": float(routing_result.estimated_cost),
                    "document_type": document_type,
                    "word_count_target": word_count_target,
                    "generated_word_count": len(result.data.split()),
                    "agent_type": "document"
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in document generation: {e}")
            raise


# Global document agent instance
document_agent = DocumentAgent()
