# Core dependencies
fastapi==0.115.6
uvicorn[standard]==0.32.1
python-dotenv==1.0.1
pydantic==2.10.3
pydantic-settings==2.7.0
httpx==0.28.1

# AI dependencies - Multi-provider support
pydantic-ai[openai]==0.0.14
pydantic-ai[anthropic]==0.0.14
pydantic-ai[google]==0.0.14
pydantic-ai[groq]==0.0.14
pydantic-ai[mistral]==0.0.14
pydantic-ai[cohere]==0.0.14
pydantic-ai[huggingface]==0.0.14

# Additional provider dependencies for compatibility
openai>=1.0.0  # For OpenRouter and DeepSeek compatibility
anthropic>=0.25.0
google-generativeai>=0.8.0
groq>=0.4.0

# Development dependencies
pytest==8.3.4
pytest-asyncio==0.24.0
black==24.10.0
isort==5.13.2
mypy==1.13.0

# Optional dependencies for enhanced functionality
python-multipart==0.0.17
jinja2==3.1.4

# Foundation Phase dependencies
fastapi-admin==1.0.4
sqlalchemy==2.0.36
alembic==1.14.0
asyncpg==0.30.0  # PostgreSQL async driver
redis==5.2.1
qdrant-client==1.12.1

# Task Analyzer & Router dependencies
route-llm==0.2.8
transformers==4.47.1
torch==2.5.1
datasets==3.2.0

# Enhanced Observability
logfire==2.5.0

# Phase 6 - Advanced Features Dependencies
# Enhanced RAG capabilities
sentence-transformers==3.3.1
langchain==0.3.10
langchain-community==0.3.10
pypdf==5.1.0
python-docx==1.1.2
unstructured==0.16.9

# Multi-modal processing
pillow==11.0.0
opencv-python==*********
moviepy==2.1.1

# Workflow automation
celery==5.4.0
aiofiles==24.1.0
httpx==0.28.1

# Performance optimizations
aiocache==0.12.3
asyncio-throttle==1.0.2
