"""
Configuration management for the Pydantic AI application with multi-provider support.
"""

import os
from typing import Optional, List, Dict, Any, Literal
from enum import Enum

from pydantic_settings import BaseSettings
from pydantic import Field, BaseModel, validator


class ProviderType(str, Enum):
    """Supported AI providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    GROQ = "groq"
    MISTRAL = "mistral"
    COHERE = "cohere"
    HUGGINGFACE = "huggingface"
    OPENROUTER = "openrouter"
    DEEPSEEK = "deepseek"
    TOGETHER = "together"


class ProviderConfig(BaseModel):
    """Base configuration for AI providers."""
    provider_type: ProviderType
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    enabled: bool = True
    priority: int = 1  # Lower numbers = higher priority
    rate_limit_rpm: Optional[int] = None  # Requests per minute
    rate_limit_tpm: Optional[int] = None  # Tokens per minute
    timeout: Optional[float] = None
    max_retries: int = 3


class OpenAIConfig(ProviderConfig):
    """OpenAI provider configuration."""
    provider_type: ProviderType = ProviderType.OPENAI
    models: List[str] = Field(default_factory=lambda: [
        "gpt-4o-mini", "gpt-4o", "gpt-3.5-turbo", "gpt-4-turbo"
    ])


class AnthropicConfig(ProviderConfig):
    """Anthropic provider configuration."""
    provider_type: ProviderType = ProviderType.ANTHROPIC
    models: List[str] = Field(default_factory=lambda: [
        "claude-3-5-sonnet-latest", "claude-3-5-haiku-latest", "claude-3-opus-latest"
    ])


class GoogleConfig(ProviderConfig):
    """Google/Gemini provider configuration."""
    provider_type: ProviderType = ProviderType.GOOGLE
    use_vertex_ai: bool = False
    project_id: Optional[str] = None
    location: Optional[str] = "us-central1"
    models: List[str] = Field(default_factory=lambda: [
        "gemini-2.0-flash", "gemini-1.5-flash", "gemini-1.5-pro"
    ])


class GroqConfig(ProviderConfig):
    """Groq provider configuration."""
    provider_type: ProviderType = ProviderType.GROQ
    models: List[str] = Field(default_factory=lambda: [
        "llama-3.3-70b-versatile", "llama-3.1-70b-versatile",
        "mixtral-8x7b-32768", "gemma2-9b-it"
    ])


class OpenRouterConfig(ProviderConfig):
    """OpenRouter provider configuration."""
    provider_type: ProviderType = ProviderType.OPENROUTER
    base_url: str = "https://openrouter.ai/api/v1"
    site_url: Optional[str] = None
    app_name: Optional[str] = None
    models: List[str] = Field(default_factory=lambda: [
        "deepseek/deepseek-chat", "deepseek/deepseek-coder",
        "anthropic/claude-3.5-sonnet", "meta-llama/llama-3.1-70b-instruct:free",
        "microsoft/wizardlm-2-8x22b", "qwen/qwen-2.5-72b-instruct"
    ])


class DeepSeekConfig(ProviderConfig):
    """DeepSeek provider configuration."""
    provider_type: ProviderType = ProviderType.DEEPSEEK
    base_url: str = "https://api.deepseek.com"
    models: List[str] = Field(default_factory=lambda: [
        "deepseek-chat", "deepseek-coder"
    ])


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Application settings
    app_name: str = Field("Pydantic AI Backend", env="APP_NAME")
    app_version: str = Field("1.0.0", env="APP_VERSION")
    debug: bool = Field(False, env="DEBUG")
    host: str = Field("0.0.0.0", env="HOST")
    port: int = Field(8000, env="PORT")

    # Legacy OpenAI API settings (for backward compatibility)
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")

    # Multi-provider API keys
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    google_api_key: Optional[str] = Field(None, env="GOOGLE_API_KEY")
    groq_api_key: Optional[str] = Field(None, env="GROQ_API_KEY")
    mistral_api_key: Optional[str] = Field(None, env="MISTRAL_API_KEY")
    cohere_api_key: Optional[str] = Field(None, env="COHERE_API_KEY")
    huggingface_api_key: Optional[str] = Field(None, env="HUGGINGFACE_API_KEY")
    openrouter_api_key: Optional[str] = Field(None, env="OPENROUTER_API_KEY")
    deepseek_api_key: Optional[str] = Field(None, env="DEEPSEEK_API_KEY")
    together_api_key: Optional[str] = Field(None, env="TOGETHER_API_KEY")

    # Provider selection and fallback
    primary_provider: ProviderType = Field(ProviderType.OPENAI, env="PRIMARY_PROVIDER")
    fallback_providers: List[ProviderType] = Field(
        default_factory=lambda: [ProviderType.GROQ, ProviderType.GOOGLE],
        env="FALLBACK_PROVIDERS"
    )
    enable_fallback: bool = Field(True, env="ENABLE_FALLBACK")

    # AI Model settings
    default_model: str = Field("gpt-4o-mini", env="DEFAULT_MODEL")
    max_tokens: int = Field(1000, env="MAX_TOKENS")
    temperature: float = Field(0.7, env="TEMPERATURE")

    # Free/low-cost model preferences
    prefer_free_models: bool = Field(True, env="PREFER_FREE_MODELS")
    free_models: List[str] = Field(
        default_factory=lambda: [
            "llama-3.3-70b-versatile",  # Groq free
            "gemini-2.0-flash",         # Google free tier
            "deepseek/deepseek-chat",   # OpenRouter free
            "meta-llama/llama-3.1-70b-instruct:free",  # OpenRouter free
        ],
        env="FREE_MODELS"
    )

    # System prompts
    default_system_prompt: str = Field(
        "You are a helpful AI assistant built with Pydantic AI. "
        "You can help with various tasks and have access to tools for calculations, "
        "weather information, and other utilities.",
        env="DEFAULT_SYSTEM_PROMPT"
    )

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    @validator('fallback_providers', pre=True)
    def parse_fallback_providers(cls, v):
        """Parse fallback providers from string or list."""
        if isinstance(v, str):
            return [ProviderType(p.strip()) for p in v.split(',') if p.strip()]
        return v

    @validator('free_models', pre=True)
    def parse_free_models(cls, v):
        """Parse free models from string or list."""
        if isinstance(v, str):
            return [m.strip() for m in v.split(',') if m.strip()]
        return v

    def get_provider_configs(self) -> Dict[ProviderType, ProviderConfig]:
        """Get provider configurations based on available API keys."""
        configs = {}

        # OpenAI
        if self.openai_api_key:
            configs[ProviderType.OPENAI] = OpenAIConfig(
                api_key=self.openai_api_key,
                priority=1 if self.primary_provider == ProviderType.OPENAI else 2
            )

        # Anthropic
        if self.anthropic_api_key:
            configs[ProviderType.ANTHROPIC] = AnthropicConfig(
                api_key=self.anthropic_api_key,
                priority=1 if self.primary_provider == ProviderType.ANTHROPIC else 3
            )

        # Google/Gemini
        if self.google_api_key:
            configs[ProviderType.GOOGLE] = GoogleConfig(
                api_key=self.google_api_key,
                priority=1 if self.primary_provider == ProviderType.GOOGLE else 4
            )

        # Groq
        if self.groq_api_key:
            configs[ProviderType.GROQ] = GroqConfig(
                api_key=self.groq_api_key,
                priority=1 if self.primary_provider == ProviderType.GROQ else 5
            )

        # OpenRouter
        if self.openrouter_api_key:
            configs[ProviderType.OPENROUTER] = OpenRouterConfig(
                api_key=self.openrouter_api_key,
                priority=1 if self.primary_provider == ProviderType.OPENROUTER else 6
            )

        # DeepSeek
        if self.deepseek_api_key:
            configs[ProviderType.DEEPSEEK] = DeepSeekConfig(
                api_key=self.deepseek_api_key,
                priority=1 if self.primary_provider == ProviderType.DEEPSEEK else 7
            )

        return configs

    def get_available_providers(self) -> List[str]:
        """Get list of available provider names."""
        return [provider.value for provider in self.get_provider_configs().keys()]


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings


def validate_settings() -> bool:
    """Validate that at least one provider API key is present."""
    provider_configs = settings.get_provider_configs()

    if not provider_configs:
        raise ValueError(
            "At least one AI provider API key is required. "
            "Please set one of: OPENAI_API_KEY, ANTHROPIC_API_KEY, GOOGLE_API_KEY, "
            "GROQ_API_KEY, OPENROUTER_API_KEY, DEEPSEEK_API_KEY in your .env file."
        )

    # Check if primary provider is available
    if settings.primary_provider not in provider_configs:
        available_providers = list(provider_configs.keys())
        if available_providers:
            print(f"Warning: Primary provider {settings.primary_provider} not available. "
                  f"Using {available_providers[0]} instead.")
        else:
            raise ValueError("No valid provider configurations found.")

    return True


def get_available_providers() -> List[ProviderType]:
    """Get list of available providers based on API keys."""
    return list(settings.get_provider_configs().keys())


def get_free_models_for_provider(provider: ProviderType) -> List[str]:
    """Get free models available for a specific provider."""
    provider_configs = settings.get_provider_configs()
    if provider not in provider_configs:
        return []

    config = provider_configs[provider]
    free_models = []

    for model in config.models:
        if any(free_model in model for free_model in settings.free_models):
            free_models.append(model)

    return free_models
