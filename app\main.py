"""
FastAPI application for Pydantic AI backend.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .agent import get_agent
from .config import get_settings, validate_settings, ProviderType, get_available_providers
from .models import (
    AgentConfig,
    ChatRequest,
    ChatResponse,
    ContextPromptRequest,
    ConversationSummary,
    ErrorResponse,
    HealthResponse,
)
from .routers import analyzer, dashboard, rag, multimodal, workflow, advanced, performance
from .dashboard import setup_admin_dashboard
from .services.observability import observability_service

# Initialize settings and validate
settings = get_settings()

try:
    validate_settings()
    available_providers = get_available_providers()
    print(f"✅ Configuration validated. Available providers: {[p.value for p in available_providers]}")
except ValueError as e:
    print(f"❌ Configuration error: {e}")
    print("Please check your .env file and ensure at least one AI provider API key is set.")

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="A modern AI backend built with Pydantic AI and FastAPI",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(analyzer.router)
app.include_router(dashboard.router)
app.include_router(rag.router)
app.include_router(multimodal.router)
app.include_router(workflow.router)
app.include_router(advanced.router)
app.include_router(performance.router)

# Setup admin dashboard
setup_admin_dashboard(app)

# Initialize observability service
logger.info("Initializing observability service...")
# The observability service is initialized when imported


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal Server Error",
            detail=str(exc) if settings.debug else "An unexpected error occurred",
            request_id=str(uuid.uuid4())
        ).dict()
    )


@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint."""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint with multi-provider status."""
    try:
        # Test agent initialization
        agent = get_agent()
        provider_info = agent.get_current_provider_info()

        return HealthResponse(
            status="healthy",
            version=settings.app_version,
            dependencies={
                "pydantic_ai": "0.0.14",
                "fastapi": "0.116.1",
                "current_provider": provider_info["provider"],
                "current_model": provider_info["model"],
                "available_providers": ",".join(provider_info["available_providers"]),
                "fallback_enabled": str(provider_info["fallback_active"]),
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Service unhealthy: {str(e)}"
        )


@app.post("/chat", response_model=ChatResponse)
async def chat_completion(request: ChatRequest):
    """
    Chat completion endpoint with multi-provider support.

    Process a user message and return an AI response with optional tool usage.
    Supports provider selection and model override per request.
    """
    try:
        agent = get_agent()

        # Convert provider string to ProviderType if provided
        provider = None
        if request.provider:
            try:
                provider = ProviderType(request.provider)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid provider: {request.provider}. Available providers: {[p.value for p in get_available_providers()]}"
                )

        response = await agent.chat(
            message=request.message,
            conversation_id=request.conversation_id,
            system_prompt=request.system_prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            provider=provider,
            model_name=request.model
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing chat request: {str(e)}"
        )


@app.post("/chat/context-prompt", response_model=ChatResponse)
async def context_prompt_chat(request: ContextPromptRequest):
    """
    Context-prompt chat completion endpoint with structured input.

    Process a context and prompt combination with intelligent provider selection.
    Uses Google Gemini as primary provider with Groq as fallback.
    """
    try:
        agent = get_agent()

        # Convert provider string to ProviderType if provided
        provider = None
        if request.provider:
            try:
                provider = ProviderType(request.provider)
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid provider: {request.provider}. Available providers: {[p.value for p in get_available_providers()]}"
                )

        # Use context as system prompt and prompt as message for optimal AI processing
        response = await agent.chat(
            message=request.prompt,
            conversation_id=request.conversation_id,
            system_prompt=request.context,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            provider=provider,
            model_name=None  # Use default model for the provider
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing context-prompt request: {str(e)}"
        )


@app.get("/conversations/{conversation_id}", response_model=ConversationSummary)
async def get_conversation(conversation_id: str):
    """Get conversation summary."""
    try:
        agent = get_agent()
        summary = agent.get_conversation_summary(conversation_id)
        
        if not summary:
            raise HTTPException(
                status_code=404,
                detail="Conversation not found"
            )
        
        return ConversationSummary(**summary)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving conversation: {str(e)}"
        )


@app.get("/conversations", response_model=List[ConversationSummary])
async def list_conversations():
    """List all conversations."""
    try:
        agent = get_agent()
        conversations = []
        
        for conv_id in agent.conversations:
            summary = agent.get_conversation_summary(conv_id)
            if summary:
                conversations.append(ConversationSummary(**summary))
        
        return conversations
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing conversations: {str(e)}"
        )


@app.post("/agent/config", response_model=Dict[str, str])
async def update_agent_config(config: AgentConfig):
    """Update agent configuration."""
    try:
        # This would update the global agent configuration
        # For now, just return success
        return {"message": "Agent configuration updated successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error updating agent configuration: {str(e)}"
        )


@app.get("/agent/config", response_model=AgentConfig)
async def get_agent_config():
    """Get current agent configuration."""
    try:
        agent = get_agent()
        return agent.config
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving agent configuration: {str(e)}"
        )


@app.get("/providers", response_model=Dict[str, Any])
async def list_providers():
    """List available AI providers and their models."""
    try:
        agent = get_agent()
        available_models = agent.get_available_models()
        current_info = agent.get_current_provider_info()
        free_models = agent.get_recommended_free_models()

        return {
            "current_provider": current_info["provider"],
            "current_model": current_info["model"],
            "fallback_enabled": current_info["fallback_active"],
            "available_providers": {
                provider.value: models
                for provider, models in available_models.items()
            },
            "recommended_free_models": free_models,
            "provider_configs": {
                "primary": settings.primary_provider.value,
                "fallbacks": [p.value for p in settings.fallback_providers],
                "prefer_free": settings.prefer_free_models
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving provider information: {str(e)}"
        )


@app.get("/tools", response_model=List[Dict[str, str]])
async def list_available_tools():
    """List available tools."""
    tools = [
        {
            "name": "calculate",
            "description": "Perform mathematical calculations",
            "example": "calculate('2 + 2')"
        },
        {
            "name": "get_weather",
            "description": "Get weather information for a location",
            "example": "get_weather('New York')"
        },
        {
            "name": "generate_uuid",
            "description": "Generate a random UUID",
            "example": "generate_uuid()"
        },
        {
            "name": "get_current_time",
            "description": "Get current timestamp",
            "example": "get_current_time()"
        },
        {
            "name": "encode_base64",
            "description": "Encode text to base64",
            "example": "encode_base64('hello world')"
        },
        {
            "name": "decode_base64",
            "description": "Decode base64 text",
            "example": "decode_base64('aGVsbG8gd29ybGQ=')"
        },
        {
            "name": "generate_password",
            "description": "Generate a random password",
            "example": "generate_password(16)"
        },
        {
            "name": "count_words",
            "description": "Count words, characters, and lines in text",
            "example": "count_words('Hello world')"
        },
        {
            "name": "reverse_text",
            "description": "Reverse the given text",
            "example": "reverse_text('hello')"
        },
        {
            "name": "extract_emails",
            "description": "Extract email addresses from text",
            "example": "extract_emails('Contact <NAME_EMAIL>')"
        }
    ]
    
    return tools


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
