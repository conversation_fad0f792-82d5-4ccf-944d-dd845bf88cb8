"""
Image agent for vision-language model tasks.
"""

import logging
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import base64

from pydantic_ai import Agent, RunContext
from pydantic import BaseModel

from ..config import get_settings
from ..services.router_service import router_service
from ..services.observability import observability_service, SpanMetadata
from ..services.multimodal_processor import multimodal_processor
from ..providers import get_provider_factory
from ..models import ChatResponse, ChatMessage


logger = logging.getLogger(__name__)


class ImageContext(BaseModel):
    """Context for image processing tasks."""
    conversation_id: str
    image_count: int = 0
    processing_mode: str = "analysis"  # analysis, ocr, description, etc.


class ImageAgent:
    """
    Specialized agent for vision-language tasks using models like Gemini Vision or DeepSeek-VL2.
    
    This agent automatically routes to the best available vision model based on task complexity
    and cost considerations, with preference for free vision models when available.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.provider_factory = get_provider_factory()
        self.conversations: Dict[str, List[ChatMessage]] = {}
        
        # Initialize with a vision-capable model
        self.current_model = self._get_vision_model()
        
        # Create the agent
        self.agent = Agent(
            model=self.current_model,
            system_prompt=self._get_system_prompt(),
        )
        
        self._register_tools()
    
    def _get_vision_model(self):
        """Get the best available vision model."""
        # Prefer free vision models
        vision_models = [
            ("google", "gemini-2.0-flash"),  # Free with vision
            ("google", "gemini-1.5-flash"),  # Free tier with vision
            ("openrouter", "qwen2.5-vl-72b-free"),  # Free vision model
            ("openai", "gpt-4o"),  # Paid fallback
            ("anthropic", "claude-3-5-sonnet-latest")  # Paid fallback
        ]
        
        for provider, model in vision_models:
            try:
                return self.provider_factory.create_model(
                    provider_type=provider,
                    model_name=model
                )
            except Exception as e:
                logger.warning(f"Failed to initialize {provider}:{model}: {e}")
                continue
        
        # Fallback to default model
        return self.provider_factory.create_model()
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for the image agent."""
        return """You are an advanced AI assistant specialized in vision and image analysis tasks.

Your capabilities include:
- Image description and analysis
- Object detection and recognition
- Text extraction (OCR) from images
- Scene understanding and interpretation
- Visual question answering
- Image comparison and analysis
- Chart and diagram interpretation
- Medical image analysis (when appropriate)
- Art and creative content analysis

When processing images:
1. Provide detailed, accurate descriptions
2. Identify key objects, people, text, and scenes
3. Answer specific questions about image content
4. Extract and transcribe any visible text
5. Explain visual relationships and context
6. Be precise about what you can and cannot see

I automatically select the most cost-efficient vision model for each task while maintaining high quality results.
"""
    
    def _register_tools(self):
        """Register image processing tools."""
        
        @self.agent.tool
        def analyze_image_composition(ctx: RunContext[ImageContext], focus_area: str) -> str:
            """Analyze specific aspects of image composition."""
            return f"Analyzing image composition with focus on: {focus_area}"
        
        @self.agent.tool
        def extract_text_regions(ctx: RunContext[ImageContext]) -> str:
            """Extract and locate text regions in the image."""
            return "Extracting text regions from the image..."
        
        @self.agent.tool
        def compare_images(ctx: RunContext[ImageContext], comparison_type: str) -> str:
            """Compare multiple images in the conversation."""
            if ctx.deps.image_count < 2:
                return "Need at least 2 images to perform comparison."
            return f"Comparing images using {comparison_type} analysis..."
        
        @self.agent.tool
        def detect_objects(ctx: RunContext[ImageContext], object_type: Optional[str] = None) -> str:
            """Detect and count specific objects in the image."""
            if object_type:
                return f"Detecting {object_type} objects in the image..."
            return "Performing general object detection..."
    
    async def process_image(
        self,
        message: str,
        image_data: Union[str, bytes],
        conversation_id: str,
        image_format: str = "auto",
        processing_mode: str = "analysis",
        **kwargs
    ) -> ChatResponse:
        """
        Process an image with accompanying text prompt.
        
        Args:
            message: Text prompt/question about the image
            image_data: Image data (base64 string or bytes)
            conversation_id: Conversation identifier
            image_format: Image format (jpeg, png, etc.)
            processing_mode: Type of processing (analysis, ocr, description, etc.)
            **kwargs: Additional parameters
            
        Returns:
            ChatResponse with vision model analysis
        """
        try:
            # Enhance prompt for vision tasks
            vision_prompt = f"Image processing task ({processing_mode}): {message}"
            
            # Analyze task complexity for vision tasks
            routing_result = router_service.pick(
                text=vision_prompt,
                modality="image",
                preferred_provider=kwargs.get('preferred_provider'),
                max_cost=kwargs.get('max_cost')
            )
            
            # Update model if routing suggests a different vision model
            if routing_result.selected_model != getattr(self.current_model, 'model_name', ''):
                logger.info(f"Image agent switching to model: {routing_result.selected_model}")
                try:
                    self.current_model = self.provider_factory.create_model(
                        provider_type=routing_result.provider,
                        model_name=routing_result.selected_model
                    )
                    
                    # Update agent with new model
                    self.agent = Agent(
                        model=self.current_model,
                        system_prompt=self._get_system_prompt(),
                    )
                    self._register_tools()
                except Exception as e:
                    logger.warning(f"Failed to switch to {routing_result.selected_model}, using current model: {e}")
            
            # Create image context
            context = ImageContext(
                conversation_id=conversation_id,
                image_count=1,
                processing_mode=processing_mode
            )
            
            # Prepare image data
            if isinstance(image_data, bytes):
                image_b64 = base64.b64encode(image_data).decode('utf-8')
            else:
                image_b64 = image_data
            
            # Store user message with image
            user_message = ChatMessage(
                role="user", 
                content=message,
                metadata={"has_image": True, "processing_mode": processing_mode}
            )
            if conversation_id not in self.conversations:
                self.conversations[conversation_id] = []
            self.conversations[conversation_id].append(user_message)
            
            # Process with vision model
            # Note: The actual image processing would depend on the specific model's API
            # This is a simplified version - real implementation would pass image data to the model
            result = await self.agent.run(
                f"{message}\n\n[Image provided for analysis - {processing_mode} mode]",
                deps=context
            )
            
            # Store assistant message
            assistant_message = ChatMessage(role="assistant", content=result.data)
            self.conversations[conversation_id].append(assistant_message)
            
            # Create response
            usage = result.usage()
            response = ChatResponse(
                response=result.data,
                conversation_id=conversation_id,
                model_used=f"{routing_result.provider}:{routing_result.selected_model}",
                tokens_used=usage.total_tokens,
                metadata={
                    "routing_reason": routing_result.reason,
                    "estimated_cost": float(routing_result.estimated_cost),
                    "fallback_used": routing_result.fallback_used,
                    "vision_task": True,
                    "processing_mode": processing_mode,
                    "image_format": image_format,
                    "model_type": "vision"
                }
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in image agent: {e}")
            raise
    
    async def describe_image(
        self,
        image_data: Union[str, bytes],
        conversation_id: str,
        detail_level: str = "standard",
        **kwargs
    ) -> ChatResponse:
        """
        Generate a description of an image.
        
        Args:
            image_data: Image data (base64 string or bytes)
            conversation_id: Conversation identifier
            detail_level: Level of detail (brief, standard, detailed)
            **kwargs: Additional parameters
        """
        prompts = {
            "brief": "Provide a brief, one-sentence description of this image.",
            "standard": "Describe this image in detail, including objects, people, setting, and activities.",
            "detailed": "Provide a comprehensive analysis of this image, including composition, colors, mood, style, and any notable details."
        }
        
        prompt = prompts.get(detail_level, prompts["standard"])
        
        return await self.process_image(
            message=prompt,
            image_data=image_data,
            conversation_id=conversation_id,
            processing_mode="description",
            **kwargs
        )
    
    async def extract_text(
        self,
        image_data: Union[str, bytes],
        conversation_id: str,
        **kwargs
    ) -> ChatResponse:
        """
        Extract text from an image (OCR).
        
        Args:
            image_data: Image data (base64 string or bytes)
            conversation_id: Conversation identifier
            **kwargs: Additional parameters
        """
        return await self.process_image(
            message="Extract and transcribe all visible text from this image. Maintain formatting and structure where possible.",
            image_data=image_data,
            conversation_id=conversation_id,
            processing_mode="ocr",
            **kwargs
        )
    
    async def answer_visual_question(
        self,
        question: str,
        image_data: Union[str, bytes],
        conversation_id: str,
        **kwargs
    ) -> ChatResponse:
        """
        Answer a specific question about an image.
        
        Args:
            question: Question about the image
            image_data: Image data (base64 string or bytes)
            conversation_id: Conversation identifier
            **kwargs: Additional parameters
        """
        return await self.process_image(
            message=f"Please answer this question about the image: {question}",
            image_data=image_data,
            conversation_id=conversation_id,
            processing_mode="vqa",
            **kwargs
        )

    async def process_media(
        self,
        file,  # UploadFile
        task_description: str = "Analyze this media file",
        conversation_id: str = "default",
        **kwargs
    ) -> Dict[str, Any]:
        """Process uploaded media file with advanced multi-modal capabilities."""
        start_time = time.time()

        metadata_span = SpanMetadata(
            operation_type="media_processing",
            file_name=file.filename,
            conversation_id=conversation_id
        )

        async with observability_service.trace_operation("image_agent_process_media", metadata_span) as span:
            try:
                # Process the media file
                processed_media = await multimodal_processor.process_media(file)

                # Generate AI-powered analysis for images
                ai_analysis = None
                if processed_media.content_type.startswith('image/'):
                    # For images, we can use the existing vision capabilities
                    ai_analysis = {
                        "description": processed_media.image_description,
                        "metadata_analysis": processed_media.metadata,
                        "ai_enhanced": True
                    }

                duration_ms = (time.time() - start_time) * 1000

                result = {
                    "status": "success",
                    "filename": file.filename,
                    "content_type": processed_media.content_type,
                    "processing_time_ms": duration_ms,
                    "media_metadata": processed_media.metadata,
                    "extracted_content": {
                        "text": processed_media.extracted_text,
                        "image_description": processed_media.image_description,
                        "audio_transcript": processed_media.audio_transcript,
                        "video_summary": processed_media.video_summary
                    },
                    "ai_analysis": ai_analysis,
                    "thumbnails": processed_media.thumbnails
                }

                # Log the processing operation
                observability_service.log_agent_execution(
                    agent_type="image",
                    request_data={
                        "operation": "media_processing",
                        "filename": file.filename,
                        "content_type": processed_media.content_type,
                        "task_description": task_description
                    },
                    response_data=result,
                    duration_ms=duration_ms,
                    model_name="multimodal_processor",
                    provider="internal"
                )

                return result

            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                logger.error(f"Error processing media file {file.filename}: {e}")

                observability_service.log_error(
                    operation_name="image_agent_process_media",
                    error=e,
                    context={
                        "filename": file.filename,
                        "task_description": task_description,
                        "duration_ms": duration_ms
                    }
                )

                return {
                    "status": "error",
                    "message": str(e),
                    "filename": file.filename,
                    "processing_time_ms": duration_ms
                }


# Global image agent instance
image_agent = ImageAgent()
